<?php
// Prevent direct access
if (!defined('AJAX_REQUEST')) {
    define('AJAX_REQUEST', true);
}

// Include init để có các hàm cần thiết
require_once '../includes/init.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if this is an AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    // Lấy 6 danh mục phổ biến
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.slug, COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.status = 1
        WHERE c.status = 1
        GROUP BY c.id, c.name, c.slug
        HAVING product_count > 0
        ORDER BY product_count DESC, c.name ASC
        LIMIT 6
    ");
    
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format data for response
    $formatted_categories = [];
    foreach ($categories as $category) {
        $formatted_categories[] = [
            'id' => (int)$category['id'],
            'name' => htmlspecialchars($category['name']),
            'slug' => htmlspecialchars($category['slug']),
            'product_count' => (int)$category['product_count']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'categories' => $formatted_categories,
        'count' => count($formatted_categories)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_popular_categories.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Có lỗi xảy ra khi tải danh mục',
        'error' => $e->getMessage()
    ]);
}
?>
